# 视频监控图层测试指南

## 问题诊断

如果视频监控图层没有显示，请按以下步骤进行诊断：

### 1. 检查浏览器控制台

打开浏览器开发者工具（F12），查看控制台是否有以下日志：

```
加载视频监控点位: 15个
图层状态: {layer: VectorLayer, htmllayer: HtmlLayer, layerShow: true, htmlLayerShow: true}
添加视频监控点位 1: 沙河大桥 114.756789 38.623456
...
视频监控点位加载完成
图层覆盖物数量: 15
```

### 2. 手动显示图层

在浏览器控制台中运行以下命令：

```javascript
// 显示视频监控图层
showVideoMonitoringLayer();

// 隐藏视频监控图层
hideVideoMonitoringLayer();
```

### 3. 检查图层控制面板

1. 打开图层管理面板
2. 找到"监测站点"分组
3. 确认"视频监控"图层的眼睛图标是否为开启状态（蓝色）
4. 如果是关闭状态（灰色），点击开启

### 4. 检查地图缩放级别

视频监控点位可能需要适当的缩放级别才能看到：

1. 缩放到曲阳县区域（经度：114.5°-115.2°，纬度：38.4°-38.9°）
2. 建议缩放级别在10-15之间

### 5. 使用调试脚本

在浏览器控制台中运行：

```javascript
// 检查图层状态
debugVideoMonitoringLayer();

// 手动创建图层（如果自动创建失败）
createVideoMonitoringLayer();

// 加载测试数据
loadVideoMonitoringData();
```

## 常见问题解决方案

### 问题1：图层不显示
**解决方案：**
1. 确认图层控制面板中视频监控图层已开启
2. 在控制台运行 `showVideoMonitoringLayer()`
3. 检查地图缩放级别和位置

### 问题2：点位无法点击
**解决方案：**
1. 确认图层已正确加载
2. 检查是否有其他图层遮挡
3. 验证点击事件是否正确绑定

### 问题3：悬停信息不显示
**解决方案：**
1. 确认HTML图层已显示
2. 检查CSS样式是否正确加载
3. 验证鼠标事件是否正确绑定

## 预期效果

正常情况下，您应该能看到：

1. **15个摄像头图标**分布在曲阳县区域内
2. **点击图标**会打开视频监控面板
3. **鼠标悬停**会显示监控点详细信息
4. **图层控制**可以正常显示/隐藏图层

## 技术参数

- **监控点数量：** 15个
- **覆盖区域：** 河北省保定市曲阳县
- **经度范围：** 114.5° - 115.2°
- **纬度范围：** 38.4° - 38.9°
- **图标文件：** `/icons/marker/camera.png`
- **图层类型：** VectorLayer + HtmlLayer

## 联系支持

如果以上步骤都无法解决问题，请提供以下信息：

1. 浏览器控制台的完整错误日志
2. 图层控制面板的截图
3. 地图当前的缩放级别和中心点坐标
4. 运行 `debugVideoMonitoringLayer()` 的输出结果
