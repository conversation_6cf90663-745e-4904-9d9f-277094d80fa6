# 视频监控图层功能说明

## 概述
在地图组件的图层管理中新增了视频监控图层，用于显示河北省保定市曲阳县区域内的视频监控点位。

## 功能特性

### 1. 图层控制
- 位置：图层管理面板 → 监测站点 → 视频监控
- 默认状态：隐藏（用户可手动开启）
- 支持显示/隐藏切换

### 2. 监控点位显示
- **图标**：摄像头图标 (`/icons/marker/camera.png`)
- **位置**：曲阳县区域内（经度：114.5°-115.2°，纬度：38.4°-38.9°）
- **数量**：15个监控点位

### 3. 监控类型
- 河道监控：沙河河道监控点
- 水库监控：曲阳水库、郎家庄水库等
- 闸门监控：恒山闸、羊平镇水闸等
- 桥梁监控：沙河大桥、北岳庙桥等
- 其他监控：县城防洪点、各镇防洪监控点

### 4. 交互功能

#### 点击事件
- 点击监控点位打开视频监控面板
- 传递参数：
  - `STCD`: 站点编码
  - `STNM`: 站点名称
  - `STTP`: "VIDEO"
  - `data`: 完整监控点数据

#### 鼠标悬停
- 显示监控点基本信息标签
- 悬停时显示详细信息弹窗，包括：
  - 监控类型
  - 设备状态（在线/离线）
  - 安装位置

### 5. 数据结构

每个监控点包含以下信息：
```javascript
{
  id: "video_1",                    // 唯一标识
  stationCode: "QY001",            // 站点编码
  stationName: "沙河大桥",          // 站点名称
  longitude: 114.756789,           // 经度
  latitude: 38.623456,             // 纬度
  monitorType: 4,                  // 监控类型（1-5）
  monitorTypeName: "桥梁监控",      // 监控类型名称
  status: 1,                       // 设备状态（0离线，1在线）
  statusName: "在线",              // 状态名称
  installLocation: "沙河主要桥梁监控点", // 安装位置描述
  // ... 其他扩展信息
}
```

## 技术实现

### 1. Mock数据
- 文件：`src/mock/videoMonitoring.js`
- 包含15个曲阳县区域内的监控点位
- 80%在线率模拟真实环境

### 2. 图层管理
- 在 `layerControlStore.js` 中添加视频监控图层配置
- 支持图层显示/隐藏控制

### 3. 地图集成
- 在 `Map/index.vue` 中实现监控点位渲染
- 添加交互事件处理
- 集成到现有图层管理系统

### 4. 样式设计
- 蓝色主题的监控点标签
- 悬停时显示详细信息弹窗
- 响应式设计，适配不同缩放级别

## 使用方法

1. **开启图层**
   - 打开图层管理面板
   - 找到"监测站点"分组
   - 点击"视频监控"图层的眼睛图标

2. **查看监控点**
   - 地图上会显示摄像头图标
   - 鼠标悬停查看基本信息
   - 点击打开详细监控面板

3. **切换显示**
   - 再次点击眼睛图标可隐藏图层
   - 支持与其他图层同时显示

## 扩展说明

### 切换到真实API
当真实接口可用时，只需在 `getVideoMonitoringList` 函数中：
1. 注释掉mock数据行
2. 取消注释真实API调用代码

```javascript
// 使用mock数据（暂时替代真实API调用）
// const res = mockVideoMonitoringResponse;

// 如果需要使用真实API，取消注释下面的代码
const res = await getVideoMonitorStationList({
  pageNum: 1,
  pageSize: 999,
});
```

### 自定义监控点
可以在 `src/mock/videoMonitoring.js` 中修改 `LOCATIONS` 数组来添加或修改监控点位信息。

## 故障排除

### 常见问题

1. **图层不显示**
   - 检查图层是否已开启（图层管理面板中的眼睛图标）
   - 确认地图缩放级别合适（建议缩放到曲阳县区域）
   - 检查浏览器控制台是否有错误信息

2. **点位无法点击**
   - 确认图层已正确加载
   - 检查是否有其他图层遮挡
   - 验证点击事件是否正确绑定

3. **图层加载失败**
   - 检查mock数据是否正确导入
   - 确认图层创建时机是否合适
   - 查看控制台错误信息

### 调试方法

1. **使用调试脚本**
   ```javascript
   // 在浏览器控制台中运行
   debugVideoMonitoringLayer(); // 检查图层状态
   ```

2. **手动创建图层**
   ```javascript
   // 如果自动创建失败，可以手动创建
   createVideoMonitoringLayer();
   ```

3. **加载测试数据**
   ```javascript
   // 加载一个测试点位验证功能
   loadVideoMonitoringData();
   ```

### 错误代码说明

- `Cannot read properties of undefined (reading 'addOverlay')`: 图层对象未正确创建
- `视频监控图层未找到`: 图层创建时机问题或名称不匹配
- `获取视频监控列表失败`: API调用或数据处理错误

## 注意事项

1. 监控点位坐标已限制在曲阳县区域内
2. 默认图层为隐藏状态，需要用户手动开启
3. 设备状态和监控类型都有对应的文本显示
4. 支持夜间模式和不同地图缩放级别
5. 与现有图层系统完全兼容，不影响其他功能
6. 图层创建有延迟机制，确保地图完全初始化后再加载数据
