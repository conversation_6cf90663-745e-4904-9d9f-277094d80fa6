import request from '@/utils/request'
import axios from 'axios'
// 行政区划相关的接口
// 获取行政区树
export function getAdcdTree() {
  return request({
    url: '/system/sys/basic/admin-districts/postAdcdTree',
    method: 'get',
  })
}
export function getAdcdTree2(data) {
  return request({
    url: '/sl323/sys/basic/ads/tree',
    method: 'post',
    data: data
  })
}
export function getTreeOption(query) {
  return request({
    url: '/system/sys/basic/admin-districts/admin/tree/' + query,
    method: 'get',


  })
}
export function getPCDTree() {
  return request({
    url: '/sl323/sys/basic/ads/getAdcBoundLayers/210000',
    method: 'get',
  })
}

// 根据adcd获取边界数据 和子
export function getAdcBoundLayers(adcd) {
  return request({
    url: '/sl323/sys/basic/ads/getAdcBoundLayers/' + adcd,
    method: 'get'
  })
}
// 根据adcd获取边界数据
export function getAdcBoundLayer(adcd) {
  return request({
    url: '/sl323/sys/basic/ads/getAdcBoundLayers/sig/' + adcd,
    method: 'get'
  })
}
export function getAdcBoundLayerByTD(keyword = '', level = 0) {
  return new Promise(async (resolve) => {
    let res =
      await axios.get(`https://api.tianditu.gov.cn/v2/administrative?keyword=${keyword}&childLevel=${level}&extensions=true&tk=86dc8be8c8a491d9abdf50a40b111862`)
    resolve(res)
  })
}
// 根据adcd获取流域边界数据
export function getLYBoundLayers(lycode) {
  return request({
    url: '/sl323/sys/basic/ads/getLYBoundLayers/sig/' + lycode,
    method: 'get'
  })
}
// 根据关键字查询
export function getStByName(data) {
  return request({
    url: '/sl323/basic/st/filter',
    method: 'post',
    data: data
  })
}
// 根据获取等值面数据
export function getContourData(data) {
  return request({
    url: '/hydro/analysis/contour/result',
    method: 'post',
    data: data
  })
}
// 气象右侧面板-风险点接口
export function getRiskEffect(query) {
  return request({
    url: '/sl323/sys/basic/ads/riskEffect',
    method: 'get',
    params: query
  })
}
// 气象右侧面板-降雨网格值
export function getRainfallGridList(query) {
  return request({
    url: '/hydro/ybwg/getRainfallGridList',
    method: 'get',
    params: query
  })
}
// // 气象右侧面板-降雨网格shp
// export function getRainfallGridGeojson(query) {
//   return request({
//     url: '/sl323/ybwg/getRainfallGridGeojson',
//     method: 'get',
//     params:query
//   })
// }
// 气象右侧面板-降雨网格shp
export function getRainfallGridGeojson(query) {
  return request({
    url: '/hydro/ybwg/getRainfallGridGeojson',
    method: 'get',
    params: query
  })
}
// 根据方案ID获取 和流域lycode 获取蓄滞洪区
export function getXzhqByYbid(query) {
  return request({
    url: '/sl323/fn/forecast/getXzhqByYbid',
    method: 'get',
    params: query
  })
}
// 调度方案结果中水库/河道站信息
export function getDisStatResList(query) {
  return request({
    url: '/sl323/fn/dispatch/getDisStatResList',
    method: 'get',
    params: query
  })
}
// 获取所有测站基础信息
export function getAllStations(query) {
  return request({
    url: '/sl323/basic/st/',
    method: 'get',
    params: query
  })
}
// // 右侧面板-区域选择
// export function getAdcBoundLayers(query) {
//   return request({
//     url: '/sl323/sys/basic/ads/getAdcBoundLayers',
//     method: 'get',
//     params:query
//   })
// }

// 针对行政区的增删改查
export function addAdcd(data) {
  return request({
    url: '/system/sys/basic/admin-districts',
    method: 'post',
    data: data
  })
}
export function updateAdcd(data) {
  return request({
    url: '/system/sys/basic/admin-districts/' + data.adcd,
    method: 'post',
    data: data
  })
}
export function delAdcd(adcd) {
  return request({
    url: '/system/sys/basic/admin-districts/' + adcd,
    method: 'delete'
  })
}
export function selecAdcdtInfo(adcd) {
  return request({
    url: '/system/sys/basic/admin-districts/' + adcd,
    method: 'get'
  })
}
export function batchStatus(data) {
  return request({
    url: '/system/sys/basic/admin-districts/batch/status',
    method: 'POST',
    data: data
  })
}

// 行政区管理里面的列表
export function getADCDNextList(adcd) {
  return request({
    url: '/sl323/sys/basic/ads/selectNextList/' + adcd,
    method: 'get',
  })
}
export function selectAdcdList(query) {
  return request({
    url: '/system/sys/basic/admin-districts',
    method: 'get',
    // data:data
    params: query

  })
}


// 针对流域的增删改查
export function selectStlyList(data) {
  return request({
    url: '/system/sys-basin/basins',
    method: 'post',
    data: data
  })
}

/**
 * 查询已生成预报方案的流域
 */
export function getForecastSchemeBasinList() {
  return request({
    url: '/system/forecast-scheme/basin',
    method: 'get'
  })
}

export function addLy(data) {
  return request({
    url: '/system/sys-basin',
    method: 'post',
    data: data
  })
}
export function updateLy(data) {
  return request({
    url: '/system/sys-basin/' + data.basinId,
    method: 'post',
    data: data
  })
}
export function delLy(basinId) {
  return request({
    url: '/system/sys-basin/' + basinId,
    method: 'delete'
  })
}
export function selectStlyInfo(basinId) {
  return request({
    url: '/system/sys-basin/' + basinId,
    method: 'get'
  })
}
// 查询淹没网格基础json
export function getYmGridGeojson(query) {
  return request({
    url: '/sl323/ybwg/getYmGridGeojson',
    method: 'get',
    params: query
  })
}// 根据调度ID 查询淹没网格数据
export function getYmGridWaterDepthGeojson(query) {
  return request({
    url: '/sl323/ybwg/getYmGridWaterDepthGeojson',
    method: 'get',
    params: query,
    timeout: 20000
  })
}
export function converToGeojson(data) {
  return request({
    url: '/sl323/sys/basic/ads/converToGeojson/',
    method: 'post',
    data: data
  })
}
export function getAdcdTreeByAccount(query) {
  return request({
    url: '/sl323/sys/basic/ads/adcdTree/account',
    method: 'get',
    params: query
  })
}
// 针对河流的增删改查
export function selectRvList(data) {
  return request({
    url: '/system/sys-river/rivers',
    method: 'post',
    data: data
  })
}

// 查询河流带分页
export function selectRvListPage(data) {
  return request({
    url: '/system/sys-river',
    method: 'get',
    params: data
  })
}

export function addRvInfo(data) {
  return request({
    url: '/system/sys-river',
    method: 'post',
    data: data
  })
}
export function updateRvInfo(data) {
  return request({
    url: '/system/sys-river/' + data.id,
    method: 'post',
    data: data
  })
}
export function delRvInfo(data) {
  return request({
    url: '/system/sys-river/' + data.id,
    method: 'delete',
  })
}
export function getRvInfo(query) {
  return request({
    url: '/system/sys-river/' + query,
    method: 'get'
  })
}
// 针对乡镇的增删改查
export function selectTsList(query) {
  return request({
    url: '/system/admin/town',
    method: 'get',
    params: query
  })
}
export function addTsInfo(data) {
  return request({
    url: 'system/admin/town',
    method: 'post',
    data: data
  })
}
export function updateTsInfo(data) {
  return request({
    url: '/system/admin/town/town',
    method: 'post',
    data: data
  })
}
export function delTsInfo(data) {
  return request({
    url: '/system/admin/town/' + data.adcd,
    method: 'delete',
  })
}
export function getTsInfo(query) {
  return request({
    url: '/system/admin/town/' + query,
    method: 'get'
  })
}
// 针对水库的增删改查
export function selectRsList(data) {
  return request({
    url: '/system/reservoir/reservoirs',
    method: 'post',
    data: data
  })
}
export function addRsInfo(data) {
  return request({
    url: '/system/reservoir',
    method: 'post',
    data: data
  })
}
export function updateRsInfo(data) {
  return request({
    url: '/system/reservoir/' + data.resCode,
    method: 'post',
    data: data
  })
}
export function delRsInfo(data) {
  return request({
    url: '/system/reservoir/' + data.resCode,
    method: 'delete',
  })
}
export function getRsInfo(query) {
  return request({
    url: '/system/reservoir/' + query,
    method: 'get'
  })
}
// 针对测站的增删改查
export function selectStaList(data) {
  return request({
    url: '/hydro/station/stations',
    method: 'post',
    data: data
  })
}
export function addStaInfo(data) {
  return request({
    url: '/system/station/station-sys',
    method: 'post',
    data: data
  })
}
export function updateStaInfo(data) {
  return request({
    url: '/system/station/station-sys/info',
    method: 'post',
    data: data
  })
}
export function delStaInfo(data) {
  return request({
    url: '/hydro/station/' + data.stcd,
    method: 'delete',
  })
}
export function getStaInfo(query) {
  return request({
    url: '/hydro/station/' + query,
    method: 'get'
  })
}

// 针对测站的增删改查
export function selectCorList(data) {
  return request({
    url: '/system/reservoir/resStations',
    method: 'post',
    data: data
  })
}
// 绑定
export function relationStcd(data) {
  return request({
    url: '/system/reservoir/res/stations',
    method: 'post',
    data: data
  })
}
// 数据测试
export function testCorInfo(data) {
  return request({
    url: '/hydro/station/station-items/' + data.stcd + '/' + data.monitorItems,
    method: 'get',
  })
}
// 详情
export function getCorInfo(resCode) {
  return request({
    url: '/system/reservoir/resStation/' + resCode,
    method: 'get'
  })
}
// 水库防洪指标分页
export function selectResFloList(data) {
  return request({
    url: '/hydro/station/flood/controlPage',
    method: 'post',
    data: data
  })
}
// 详情
export function getResFloInfo(stcd) {
  return request({
    url: '/hydro/station/flood/control/' + stcd,
    method: 'get'
  })
}
// 水库防洪指标保存
export function controlResFlo(data) {
  return request({
    url: '/hydro/station/flood/control',
    method: 'post',
    data: data
  })
}


// 河道防洪指标分页
export function selectRvFloList(data) {
  return request({
    url: '/hydro/station/river/floodPage',
    method: 'post',
    data: data
  })
}
// 详情
export function getRvFloInfo(stcd) {
  return request({
    url: '/hydro/station/river/flood/' + stcd,
    method: 'get'
  })
}
// 河道防洪指标保存
export function controlRvFlo(data) {
  return request({
    url: '/hydro/station/river/flood',
    method: 'post',
    data: data
  })
}

// 水雨情测站权限分页查询
export function selectAuthList(data) {
  return request({
    url: '/system/station/station-auth-page',
    method: 'post',
    data: data
  })
}
// 详情
export function getStaTenInfo(stcd) {
  return request({
    url: '/system/station/station-view/' + stcd,
    method: 'get'
  })
}
// 水雨情测站权限绑定
export function saveAuthList(data) {
  return request({
    url: '/system/station/station-view',
    method: 'post',
    data: data
  })
}

// 乡镇雨量站权重关系
export function saveStationWeights(data) {
  return request({
    url: '/system/admin/town/station-weights',
    method: 'post',
    data: data
  })
}

export function getStationWeights(adcd) {
  return request({
    url: `/system/admin/town/station-weights/${adcd}`,
    method: 'get'
  })
}

/**
 * 水闸信息列表
 */
export function fetchSluiceGateList(data) {
  return request({
    url: '/system/water-gate',
    method: 'get',
    params: data
  })
}

/**
 * 水闸信息详情
 */
export function fetchSluiceGateInfo(code) {
  return request({
    url: '/system/water-gate/' + code,
    method: 'get'
  })
}

/**
 * 水闸新增和编辑
 */
export function saveSluiceGate(data) {
  return request({
    url: '/system/water-gate',
    method: 'post',
    data: data
  })
}

/**
 * 水闸删除
 */
export function deleteSluiceGate(id) {
  return request({
    url: '/system/water-gate/' + id,
    method: 'delete'
  })
}

/**
 * 安全监测站列表
 */
export function fetchSafetyMonitoringStationList(data) {
  return request({
    url: '/system/safety-monitor-station',
    method: 'get',
    params: data
  })
}

/**
 * 安全监测站详情
 */
export function fetchSafetyMonitoringStationInfo(id) {
  return request({
    url: '/system/safety-monitor-station/' + id,
    method: 'get'
  })
}

/**
 * 安全监测站新增和编辑
 */
export function saveSafetyMonitoringStation(data) {
  return request({
    url: '/system/safety-monitor-station',
    method: 'post',
    data: data
  })
}

/**
 * 安全监测站删除
 */
export function deleteSafetyStation(id) {
  return request({
    url: '/system/safety-monitor-station/' + id,
    method: 'delete'
  })
}


/**
 * 安全监测站的监测点列表
 */
export function fetchSafetyMonitoringStationPointList(params) {
  return request({
    url: '/system/safety-monitor-station/list/' + params.stationCode,
    method: 'get',
    params: params
  })
}

/**
 * 安全监测断面列表
 */
export function fetchSafetyMonitoringSectionList(data) {
  return request({
    url: '/system/monitor-section',
    method: 'get',
    params: data
  })
}

/**
 * 安全监测断面新增和编辑
 */
export function saveSafetyMonitoringSection(data) {
  return request({
    url: '/system/monitor-section',
    method: 'post',
    data: data
  })
}

/**
 * 安全监测断面删除
 */
export function deleteSafetyMonitoringSection(id) {
  return request({
    url: '/system/monitor-section/' + id,
    method: 'delete'
  })
}

/**
 * 安全监测断面的监测点信息列表
 */
export function fetchSafetyMonitoringSectionPointList(id) {
  return request({
    url: '/system/monitor-section/list/' + id,
    method: 'get'
  })
}

/**
 * 安全监测设备列表
 */
export function fetchSafetyMonitoringEquipmentList(data) {
  return request({
    url: '/system/safety-monitor-device',
    method: 'get',
    params: data
  })
}

/**
 * 安全监测设备详情
 */
export function fetchSafetyMonitoringEquipmentInfo(id) {
  return request({
    url: '/system/safety-monitor-device/' + id,
    method: 'get'
  })
}

/**
 * 安全监测设备新增和编辑
 */
export function saveSafetyMonitoringEquipment(data) {
  return request({
    url: '/system/safety-monitor-device',
    method: 'post',
    data: data
  })
}

/**
 * 安全监测设备删除
 */
export function deleteSafetyMonitoringEquipment(id) {
  return request({
    url: '/system/safety-monitor-device/' + id,
    method: 'delete'
  })
}

/**
 * 安全监测点列表
 */
export function fetchSafetyMonitoringPointList(data) {
  // 发送请求，获取安全监控区域列表
  return request({
    url: '/system/safety-monitor-point',
    method: 'get',
    params: data
  })
}

/**
 * 安全监测点新增和编辑
 */
export function saveSafetyMonitoringPoint(data) {
  return request({
    url: '/system/safety-monitor-point',
    method: 'post',
    data: data
  })
}

/**
 * 获取安全监测点监测点详细信息
 */
export function fetchSafetyMonitoringPointDetail(id) {
  return request({
    url: '/system/safety-monitor-point/' + id,
    method: 'get'
  })
}

/**
 * 安全监测点删除
 */
export function deleteSafetyMonitoringPoint(id) {
  return request({
    url: '/system/safety-monitor-point/' + id,
    method: 'delete'
  })
}

/**
 * 通过阿里云 datav 行政区划接口获取 geojson
 * @param {string} code - 行政区划代码，如 '540100_full'
 * @returns {Promise} - 返回 geojson 数据
 */
export function getAliyunGeoJsonByCode(code) {
  return axios.get(`https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=${code}`);
}

// 查询水闸的偃闸水情信息
export function getWaterGateDetail(params) {
  return request({
    url: '/system/water-gate/detail',
    method: 'get',
    params
  })
}

// 查询河流List及其空间数据
export function getRiverGeoList(params) {
  return request({
    url: '/system/sys-river/geos',
    method: 'get',
    params
  })
}
