<!--
 * @Description:
 * @Author: liguiyuan
 * @LastEditors: liguiyuan
-->
<template>
  <div class="groupPanel">
    <layer-control></layer-control>
    <map-switch></map-switch>
    <base-tool></base-tool>
  </div>
</template>

<script setup>
import baseTool from "../baseTool";
import mapSwitch from "../mapSwitch";
import layerControl from "../layerControl";
import useScreenStore from "@/store/modules/screen";

defineOptions({
  name: "groupPanel",
});

const screenStore = useScreenStore();

const leftPosition = computed(() => {
  return screenStore.isPanelCollapsed ? "20px" : "400px";
});
</script>

<style scoped>
.groupPanel {
  position: absolute;
  left: v-bind(leftPosition);
  bottom: 38px;
  z-index: 17;
  height: auto;
  display: flex;
  flex-direction: column;
  transition: left 0.3s ease;
}
</style>
