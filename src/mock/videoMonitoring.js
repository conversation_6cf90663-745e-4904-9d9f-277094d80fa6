/**
 * 视频监控mock数据
 * 位置：河北省保定市曲阳县
 * 经纬度范围：
 * 经度：114.5° - 115.2°
 * 纬度：38.4° - 38.9°
 */

// 生成随机经纬度（曲阳县范围内）
const generateRandomCoordinate = () => {
  const longitude = 114.5 + Math.random() * 0.7; // 114.5 - 115.2
  const latitude = 38.4 + Math.random() * 0.5;   // 38.4 - 38.9
  return {
    longitude: parseFloat(longitude.toFixed(6)),
    latitude: parseFloat(latitude.toFixed(6))
  };
};

// 监控类型枚举
const MONITOR_TYPES = {
  1: "河道监控",
  2: "水库监控", 
  3: "闸门监控",
  4: "桥梁监控",
  5: "其他监控"
};

// 设备状态
const DEVICE_STATUS = {
  0: "离线",
  1: "在线"
};

// 曲阳县主要河流和地点
const LOCATIONS = [
  { name: "沙河大桥", type: 4, description: "沙河主要桥梁监控点" },
  { name: "曲阳水库", type: 2, description: "曲阳县主要水库监控" },
  { name: "恒山闸", type: 3, description: "恒山水闸监控点" },
  { name: "沙河河道1", type: 1, description: "沙河上游河道监控" },
  { name: "沙河河道2", type: 1, description: "沙河中游河道监控" },
  { name: "沙河河道3", type: 1, description: "沙河下游河道监控" },
  { name: "北岳庙桥", type: 4, description: "北岳庙附近桥梁监控" },
  { name: "县城防洪点", type: 5, description: "县城防洪监控点" },
  { name: "产德乡河道", type: 1, description: "产德乡河道监控点" },
  { name: "羊平镇水闸", type: 3, description: "羊平镇水闸监控" },
  { name: "灵山镇河道", type: 1, description: "灵山镇河道监控点" },
  { name: "晓林镇桥梁", type: 4, description: "晓林镇桥梁监控点" },
  { name: "郎家庄水库", type: 2, description: "郎家庄小型水库监控" },
  { name: "孝墓乡河道", type: 1, description: "孝墓乡河道监控点" },
  { name: "文德镇防洪", type: 5, description: "文德镇防洪监控点" }
];

// 生成mock数据
export const generateVideoMonitoringMockData = () => {
  return LOCATIONS.map((location, index) => {
    const coord = generateRandomCoordinate();
    const isOnline = Math.random() > 0.2; // 80%在线率
    
    return {
      id: `video_${index + 1}`,
      stationCode: `QY${String(index + 1).padStart(3, '0')}`,
      stationName: location.name,
      longitude: coord.longitude,
      latitude: coord.latitude,
      monitorType: location.type,
      monitorTypeName: MONITOR_TYPES[location.type],
      status: isOnline ? 1 : 0,
      statusName: DEVICE_STATUS[isOnline ? 1 : 0],
      installLocation: location.description,
      installDate: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      manufacturer: ["海康威视", "大华技术", "宇视科技", "华为"][Math.floor(Math.random() * 4)],
      model: `Camera-${Math.floor(Math.random() * 9000) + 1000}`,
      resolution: ["1080P", "4K", "720P"][Math.floor(Math.random() * 3)],
      district: "曲阳县",
      township: ["恒州镇", "灵山镇", "羊平镇", "产德乡", "晓林镇", "孝墓乡", "文德镇"][Math.floor(Math.random() * 7)],
      maintainer: "曲阳县水务局",
      phone: "0312-4212345",
      lastMaintenanceDate: `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      videoUrl: `rtmp://demo.server.com/live/stream_${index + 1}`, // 模拟视频流地址
      previewUrl: `/images/camera_preview_${(index % 5) + 1}.jpg`, // 模拟预览图
      // 扩展信息
      networkType: ["有线", "无线4G", "无线5G"][Math.floor(Math.random() * 3)],
      powerType: ["市电", "太阳能", "市电+备用电池"][Math.floor(Math.random() * 3)],
      storageCapacity: `${Math.floor(Math.random() * 30) + 7}天`,
      nightVision: Math.random() > 0.3, // 70%支持夜视
      weatherProof: true, // 都支持防水防尘
      ptzSupport: Math.random() > 0.5, // 50%支持云台控制
      audioSupport: Math.random() > 0.4, // 60%支持音频
      alarmSupport: Math.random() > 0.6, // 40%支持报警
      recordingStatus: isOnline ? (Math.random() > 0.1 ? "正常录制" : "录制异常") : "离线",
      lastOnlineTime: isOnline ? "实时在线" : `2024-01-${String(Math.floor(Math.random() * 30) + 1).padStart(2, '0')} ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
      // 技术参数
      viewAngle: `${Math.floor(Math.random() * 60) + 60}°`, // 60-120度视角
      zoomRatio: `${Math.floor(Math.random() * 20) + 5}倍`, // 5-25倍变焦
      minIllumination: `${(Math.random() * 0.1).toFixed(3)}Lux`,
      workingTemperature: "-30°C ~ +60°C",
      workingHumidity: "≤95%RH",
      ipRating: "IP66",
      // 网络参数
      ipAddress: `192.168.1.${Math.floor(Math.random() * 200) + 50}`,
      macAddress: `00:${Math.floor(Math.random() * 256).toString(16).padStart(2, '0').toUpperCase()}:${Math.floor(Math.random() * 256).toString(16).padStart(2, '0').toUpperCase()}:${Math.floor(Math.random() * 256).toString(16).padStart(2, '0').toUpperCase()}:${Math.floor(Math.random() * 256).toString(16).padStart(2, '0').toUpperCase()}:${Math.floor(Math.random() * 256).toString(16).padStart(2, '0').toUpperCase()}`,
      port: 8000 + Math.floor(Math.random() * 1000),
      bandwidth: `${Math.floor(Math.random() * 50) + 10}Mbps`
    };
  });
};

// 导出mock数据
export const videoMonitoringMockData = generateVideoMonitoringMockData();

// 模拟API响应格式
export const mockVideoMonitoringResponse = {
  code: 200,
  message: "success",
  data: videoMonitoringMockData,
  total: videoMonitoringMockData.length,
  pageNum: 1,
  pageSize: 999
};
