import { defineStore } from "pinia";
import { ref } from "vue";
import moment from "moment";

/**
 * 地图状态管理
 * 管理地图相关的状态：区域选择、位置定位、图层控制等
 */
const useLayerControlStore = defineStore("layer-control", () => {
  const startTime = ref("");
  const endTime = ref("");
  const currentContourLayer = ref(null);
  const dialogVisible = ref(false);

  // 图层控制状态
  const layerControls = ref([
    {
      id: 1,
      label: "河流层",
      type: "river1",
      className: "river-custom",
      visible: true,
      icon: "",
      children: [
        {
          id: 111,
          label: "干流",
          type: "river-main",
          visible: true,
          icon: "一级河流.png",
          children: [],
        },
        {
          id: 112,
          label: "一级支流",
          type: "river-branch-1st",
          visible: true,
          icon: "二级河流.png",
          children: [],
        },
        {
          id: 113,
          label: "二级支流",
          type: "river-branch-2nd",
          visible: true,
          icon: "三级河流.png",
          children: [],
        },
      ],
    },
    {
      id: 2,
      label: "监测站点",
      type: "station",
      visible: true,
      expanded: true,
      icon: "",
      children: [
        {
          id: 5,
          visible: true,
          label: "雨量站",
          type: "rain",
          icon: "rain.png",
          expanded: true,
          children: [
            {
              id: 51,
              visible: false,
              label: "无雨",
              type: "rain-no",
              icon: "rain.png",
            },
            {
              id: 52,
              visible: true,
              label: "有雨",
              type: "rain-yes",
              icon: "rain.png",
              expanded: true,
              children: [
                {
                  id: 521,
                  label: "小雨",
                  type: "rain-light",
                  color: "#BAEF9F",
                },
                {
                  id: 522,
                  label: "中雨",
                  type: "rain-moderate",
                  color: "#55DD33",
                },
                {
                  id: 523,
                  label: "大雨",
                  type: "rain-heavy",
                  color: "#7FBDFF",
                },
                {
                  id: 524,
                  label: "暴雨",
                  type: "rain-storm",
                  color: "#000DFF",
                },
                {
                  id: 525,
                  label: "大暴雨",
                  type: "rain-heavy-storm",
                  color: "#E434EF",
                },
                {
                  id: 526,
                  label: "特大暴雨",
                  type: "rain-extreme-storm",
                  color: "#7F0341",
                },
              ],
            },
          ],
        },
        {
          id: 6,
          visible: true,
          label: "河道水文（位）站",
          type: "river",
          icon: "river.png",
        },
        {
          id: 7,
          visible: true,
          label: "水库水文站",
          type: "reservoir",
          icon: "reservoir.png",
        },
        // {
        //   id: 23,
        //   visible: true,
        //   label: "水闸",
        //   type: "waterGate",
        //   icon: "zhamen.png",
        // },
      ],
    },
    {
      id: 12,
      visible: false,
      label: "村社",
      type: "township",
      icon: "house-poi.png",
    },
    {
      id: 13,
      visible: false,
      label: "企事业单位",
      type: "enterprise",
      icon: "enterprises-poi.png",
    },
    {
      id: 14,
      visible: false,
      label: "居民户",
      type: "household",
      icon: "household-poi.png",
    },
    {
      id: 15,
      visible: false,
      label: "安置点",
      type: "place",
      icon: "place-poi.png",
    },
    {
      id: 16,
      visible: false,
      label: "风险隐患",
      type: "risk",
      icon: "riskproblem-poi.png",
    },
    {
      id: 21,
      visible: false,
      label: "危险区",
      type: "hazardous",
      icon: "warning.png",
    },
    {
      id: 22,
      visible: false,
      label: "转移路线",
      type: "road",
      icon: "road.png",
    },
    {
      id: 17,
      label: "专题图",
      type: "special",
      visible: true,
      expanded: true,
      icon: "",
      children: [
        {
          id: 18,
          visible: false,
          label: "气象云图",
          type: "cloud-special",
          icon: "",
        },
        {
          id: 19,
          visible: false,
          label: "雷达回波图",
          type: "radar-special",
          icon: "",
        },
        {
          id: 20,
          visible: false,
          label: "预报降雨网格",
          type: "rainContour-special",
          icon: "",
        },
        {
          id: 10,
          visible: false,
          label: "降雨等值面",
          type: "contour",
          icon: "",
        },
      ],
    },
  ]);

  // 根据id查找图层
  const findLayerById = (layers, id) => {
    if (!layers || !id) return null;
    for (const layer of layers) {
      if (layer.id === id) {
        return layer;
      }
      if (layer.children && layer.children.length > 0) {
        const found = findLayerById(layer.children, id);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  /**
   * 更新图层控制状态
   */
  const changeVis = (data) => {
    try {
      if (!data.children) {
        data.children = [];
      }

      if (data.hasOwnProperty("treeVisible")) {
        const currentLayer = findLayerById(layerControls.value, data.id);
        if (currentLayer) {
          currentLayer.visible = data.treeVisible;
          data.visible = data.treeVisible;
        }
      } else {
        // 控制图层可见不可见
        data.visible = !data.visible;
      }

      // 处理专题图互斥选择
      // 获取专题图层
      const specialLayers =
        layerControls.value.find((item) => item.type === "special")?.children ||
        [];
      // 查找当前图层是否为专题图层
      const specialLayer = specialLayers.find(
        (item) => item.type === data.type
      );
      if (specialLayer) {
        // 如果当前图层被选中，则关闭其他专题图
        if (data.visible) {
          specialLayers.forEach((layer) => {
            if (layer.type !== data.type) {
              layer.visible = false;
            }
          });
          // 关闭其他专题图的相关功能
          window.EventBus.$emit("cloudPlay/change", null);
          window.EventBus.$emit("layerCloud/visible", false);
        }
      }

      const viewer = window.viewer;
      const layerGroup = viewer.getLayerGroup(data.type);

      if (data.type === "river-main") {
        // 控制干流图层（含线和label）
        const group = viewer.getLayerGroup("river-main-group");
        if (group) group.show = data.visible;
      } else if (data.type === "river-branch-1st") {
        // 控制一级支流图层（含线和label）
        const group = viewer.getLayerGroup("river-branch-1st-group");
        if (group) group.show = data.visible;
      } else if (data.type === "river-branch-2nd") {
        // 控制二级支流图层（含线和label）
        const group = viewer.getLayerGroup("river-branch-2nd-group");
        if (group) group.show = data.visible;
      } else if (data.type === "rain") {
        // 控制雨量站图层
        layerGroup.show = data.visible;

        // 获取所有雨量站primitive图层并控制显隐
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (layer.id.startsWith("rain-primitive")) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "rain-no") {
        // 控制无雨雨量站图层
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (
            layer.id.startsWith("rain-no-primitive") ||
            layer.id === "rain-no-layer-html"
          ) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "rain-yes") {
        // 控制有雨雨量站图层
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (
            layer.id.startsWith("rain-yes-primitive") ||
            layer.id === "rain-yes-layer-html"
          ) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "river") {
        // 控制河道站点位图层
        layerGroup.show = data.visible;
        // 获取所有村社点位primitive图层并控制显隐
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (layer.id.startsWith("river-primitive")) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "reservoir") {
        // 控制水库站点位图层
        layerGroup.show = data.visible;
        // 获取所有村社点位primitive图层并控制显隐
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (layer.id.startsWith("reservoir-primitive")) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "waterGate") {
        // 控制水闸点位图层
        // layerGroup.show = data.visible;
        // 获取所有村社点位primitive图层并控制显隐
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (layer.id.startsWith("gate-primitive")) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "township") {
        // 控制村社点位图层
        // layerGroup.show = data.visible;
        // 获取所有村社点位primitive图层并控制显隐
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (layer.id.startsWith("township-primitive")) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "enterprise") {
        // 控制企事业单位点位图层
        viewer.getLayer("enterprise-layer-html").show = data.visible;
        // 获取所有企事业单位点位primitive图层并控制显隐
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (layer.id.startsWith("enterprise-primitive")) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "household") {
        // 控制居民户点位图层
        viewer.getLayer("resident-layer-html").show = data.visible;
        // 获取所有居民户点位primitive图层并控制显隐
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (layer.id.startsWith("resident-primitive")) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "place") {
        // 控制安置点点位图层
        viewer.getLayer("place-layer-html").show = data.visible;
        // 获取所有安置点点位primitive图层并控制显隐
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (layer.id.startsWith("place-primitive")) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "risk") {
        // 控制风险隐患点位图层
        viewer.getLayer("risk-problem-layer-html").show = data.visible;
        // 获取所有风险隐患点位primitive图层并控制显隐
        const layers = viewer.getLayers();
        layers.forEach((layer) => {
          if (layer.id.startsWith("risk-problem-primitive")) {
            layer.show = data.visible;
          }
        });
      } else if (data.type === "hazardous") {
        // 控制危险区图层和图例
        viewer.getLayer("dangerArea-layer-html").show = data.visible;
        viewer.getLayer("dangerArea-layer").show = data.visible;
      } else if (data.type === "road") {
        // 控制转移路线图元的显隐
        const primitives = viewer.scene.primitives;
        for (let i = 0; i < primitives.length; i++) {
          const p = primitives.get(i);
          if (
            p._instanceIds &&
            p._instanceIds[0]?.startsWith("road-primitive-")
          ) {
            p.show = data.visible;
          }
        }
      }
      // 气象云图
      else if (data.type === "cloud-special") {
        if (data.visible) {
          window.EventBus.$emit("cloudPlay/change", 1);
          window.EventBus.$emit("change/right/height/min", {});
          // 显示面板
          window.EventBus.$emit("layerCloud/visible", true);
        } else {
          window.EventBus.$emit("cloudPlay/change", null);
          window.EventBus.$emit("layerCloud/visible", false);
        }
      }
      // 雷达回波图
      else if (data.type === "radar-special") {
        if (data.visible) {
          window.EventBus.$emit("cloudPlay/change", 2);
          window.EventBus.$emit("change/right/height/min", {});
          // 显示面板
          window.EventBus.$emit("layerCloud/visible", true);
        } else {
          window.EventBus.$emit("cloudPlay/change", null);
          window.EventBus.$emit("layerCloud/visible", false);
        }
      }
      // 预报降雨网格
      else if (data.type === "rainContour-special") {
        if (data.visible) {
          window.EventBus.$emit("cloudPlay/change", 3);
          window.EventBus.$emit("change/right/height/min", {});
        } else {
          window.EventBus.$emit("cloudPlay/change", null);
        }
      } else if (data.type === "contour") {
        console.log(data, "data");

        if (data.visible) {
          // 设置默认时间范围
          startTime.value = moment()
            .subtract(1, "hours")
            .format("YYYY-MM-DD HH:mm:ss");
          endTime.value = moment().format("YYYY-MM-DD HH:mm:ss");
          // 保存当前图层引用
          currentContourLayer.value = data;
          // 显示弹窗
          dialogVisible.value = true;

          console.log(dialogVisible.value, "dialogVisible");
        } else {
          window.EventBus.$emit("rainContour/clear");
        }
      } else {
        // 其他图层正常控制
        layerGroup.show = data.visible;
      }
    } catch (error) {
      console.error(error, "error");
    }
  };

  return {
    layerControls,
    startTime,
    endTime,
    currentContourLayer,
    dialogVisible,
    changeVis,
  };
});

export default useLayerControlStore;
