<script setup>
import { ref, onMounted, getCurrentInstance, nextTick } from "vue";
import { VueFlow, useVueFlow, MarkerType } from "@vue-flow/core";
import { Controls } from "@vue-flow/controls";
import { Background } from "@vue-flow/background";
import { selectRsList } from "@/api/watershed/ads";
import {
  getRiverSectionList,
  getSubBasinList,
  getSubBasinWeight,
} from "@/api/watershed";
import useDragAndDrop from "./useDnD";

const { proxy } = getCurrentInstance();
const {
  onConnect,
  onInit,
  addEdges,
  removeNodes,
  updateNode,
  fitView,
  setNodes,
  setEdges,
} = useVueFlow();
const emit = defineEmits(["getNodeValue"]);
const props = defineProps(["nodes", "edges", "nodeValue", "showType"]);
const { onDragOver, onDrop, onDragLeave, isDragOver } = useDragAndDrop();
const show = ref(false);
const defaultNodes = ref([]);
const defaultEdges = ref([]);
const activeIndex = ref(null);
const selectNode = ref(null);
// 小流域节点数据
const subBasinList = ref([]);
// 水库节点数据
const riverReservoirList = ref([]);
// 河道断面节点数据
const riverSectionList = ref([]);
const nodesChange = (e) => {
  console.log("nodesChange", e);
  let { type, selected } = e[0];

  if (type == "add") {
    let { item } = e[0];
    if (item.data.nodeType == "subBasin") {
      subBasinList.value.push({
        type: 1,
        nodeType: "subBasin",
        nodeOrder: item.data.nodeId,
        label: item.data.label,
        // 小流域代码
        basinCode: "",
        // 小流域标识
        basinIdentifier: "",
        // 小流域雨量站关联信息
        list: [],
      });
    }
    if (item.data.nodeType == "reservoir") {
      riverReservoirList.value.push({
        type: 2,
        nodeType: "reservoir",
        nodeOrder: item.data.nodeId,
        label: item.data.label,
        // 水库代码
        reservoirCode: "",
        // 水库标识
        reservoirIdentifier: "",
        // 水库出库方式
        releaseMethod: "",
        // 水库出库流量
        releaseFlow: "",
      });
    }
    if (item.data.nodeType == "section") {
      riverSectionList.value.push({
        type: 5,
        nodeType: "section",
        nodeOrder: item.data.nodeId,
        label: item.data.label,
        // 河道断面代码
        riverSectionCode: "",
        // 是否为入流断面
        isInflowSection: "",
        // 断面标识
        sectionIdentifier: "",
      });
    }
  } else if (type == "remove") {
    let { id } = e[0];
    let index;
    index = subBasinList.value.findIndex((el) => el.nodeOrder == id);
    if (index != -1) {
      subBasinList.value.splice(index, 1);
    }
    let index2;
    index2 = riverReservoirList.value.findIndex((el) => el.nodeOrder == id);
    if (index2 != -1) {
      riverReservoirList.value.splice(index2, 1);
    }

    let index3;
    index3 = riverSectionList.value.findIndex((el) => el.nodeOrder == id);
    console.log(index3);
    if (index3 != -1) {
      riverSectionList.value.splice(index3, 1);
    }
  }
  if (type === "select") {
    if (selected) {
      const selected = e.filter((item) => item.selected);
      const { id } = selected[0];
      selectNode.value = id;
    }
  }
};

const rules1 = {
  label: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  basinCode: [{ required: true, message: "请选择小流域", trigger: "change" }],
  basinIdentifier: [
    { required: true, message: "请输入小流域标识", trigger: "blur" },
  ],
};
const rules2 = {
  label: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  reservoirCode: [{ required: true, message: "请选择水库", trigger: "change" }],
  reservoirIdentifier: [
    { required: true, message: "请输入水库标识", trigger: "blur" },
  ],
  releaseMethod: [
    { required: true, message: "请选择出库方式", trigger: "change" },
  ],
  releaseFlow: [{ required: true, message: "请输入出库流量", trigger: "blur" }],
};
const rules3 = {
  label: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  riverSectionCode: [
    { required: true, message: "请选择河道断面", trigger: "change" },
  ],
  sectionIdentifier: [
    { required: true, message: "请输入断面标识", trigger: "blur" },
  ],
  isInflowSection: [
    { required: true, message: "请选择是否为入流断面", trigger: "change" },
  ],
};

/**
 * 等待 Vue Flow 渲染完成并自动适应视图
 */
const waitForFlowRenderAndFitView = async () => {
  // 等待 Vue 响应式更新
  await nextTick();

  // 等待 DOM 渲染完成
  await new Promise((resolve) => {
    const checkRender = () => {
      const flowElement = proxy.$refs.dragFlow?.$el;
      const hasFlowDOM = flowElement?.querySelector(
        ".vue-flow__nodes, .vue-flow__edges"
      );

      if (hasFlowDOM) {
        // 使用 requestAnimationFrame 确保渲染完成
        requestAnimationFrame(() => requestAnimationFrame(resolve));
      } else {
        // 如果 DOM 还没准备好，继续等待
        setTimeout(checkRender, 50);
      }
    };
    checkRender();
  });

  // 执行视图适应
  try {
    fitView({
      duration: 600,
      padding: 0.1,
    });
  } catch (error) {
    console.warn("自动适应视图失败:", error);
  }
};

const showView = () => {
  // 重置数据
  subBasinList.value = [];
  riverReservoirList.value = [];
  riverSectionList.value = [];

  // 等待数据重置完成
  nextTick(() => {
    // 处理节点数据
    props.nodeValue.map((item) => {
      if (item.type == 1) {
        subBasinList.value.push(item);
      } else if (item.type == 2) {
        riverReservoirList.value.push(item);
      } else if (item.type == 5) {
        riverSectionList.value.push(item);
      }
    });

    // 设置节点，并更新节点显示内容
    defaultNodes.value = props.nodes.map((node) => {
      const updatedNode = { ...node };

      // 根据节点类型更新显示内容
      if (node.data.nodeType === "subBasin") {
        // 查找对应的小流域数据
        const subBasinData = subBasinList.value.find(
          (item) => item.nodeOrder === node.id
        );
        if (subBasinData) {
          // 显示格式：小流域名称/累计面雨量
          const displayText = `${
            subBasinData.label || subBasinData.basinName || "小流域"
          } / ${subBasinData.totalRainfall || 0}mm`;
          updatedNode.data = {
            ...updatedNode.data,
            label: displayText,
          };
        }
      } else if (node.data.nodeType === "section") {
        // 查找对应的河道断面数据
        const sectionData = riverSectionList.value.find(
          (item) => item.nodeOrder === node.id
        );
        if (sectionData) {
          // 显示格式：河道断面名称/洪峰流量
          const displayText = `${
            sectionData.label || sectionData.sectionIdentifier || "河道断面"
          } / ${sectionData.peakInflow || 0}m³/s`;
          updatedNode.data = {
            ...updatedNode.data,
            label: displayText,
          };
        }
      }

      return updatedNode;
    });

    // 处理边数据
    const newEdges = props.edges.map((item) => ({
      id: item.id,
      source: item.source,
      target: item.target,
      markerEnd: MarkerType.ArrowClosed,
    }));
    defaultEdges.value = newEdges;

    waitForFlowRenderAndFitView();
  });
};

// 小流域列表
const waterAreaList = ref([]);
// 水库列表
const waterPointList = ref([]);
// 河道断面列表
const waterSectionList = ref([]);
const form1Check = ref(false);
const form2Check = ref(false);
const form3Check = ref(false);
const dbclickNodeId = ref(null);

// 获取河道断面
const getAllarea = async () => {
  const res = await getRiverSectionList({ pageNum: 1, pageSize: 999 });
  if (res.code === 200) {
    waterSectionList.value = res.rows || [];
  }
};

// 获取小流域
const getAllWater = async () => {
  let res = await getSubBasinList({ pageNum: 1, pageSize: 999 });
  if (res.code === 200) {
    waterAreaList.value = res.rows || [];
  }
};

// 获取水库
const getAllWaterPoint = async () => {
  let res = await selectRsList({ pageNum: 1, pageSize: 999 });
  waterPointList.value = res.data.records || [];
};

const changeBasin = async (code) => {
  const res = await getSubBasinWeight(code);
  if (res.code === 200) {
    subBasinList.value[activeIndex.value].list = res.data.weightPOS;
  }
};

// 重置节点
const resetNodeConfirm = () => {
  // 确认重置操作
  proxy.$modal
    .confirm(
      "确定要重置所有节点吗？此操作将清空画布上的所有节点和连接线，且无法撤销。"
    )
    .then(() => {
      resetNode();
    })
    .catch(() => {
      // 用户取消操作
      console.log("用户取消重置操作");
    });
};

const resetNode = (notify = true) => {
  // 清空画布上的所有节点和边
  setNodes([]);
  setEdges([]);

  // 重置默认节点和边数据
  defaultNodes.value = [];
  defaultEdges.value = [];

  // 重置所有相关的数据状态
  subBasinList.value = [];
  riverReservoirList.value = [];
  riverSectionList.value = [];

  // 重置选中状态
  selectNode.value = null;
  activeIndex.value = null;

  // 重置表单验证状态
  form1Check.value = false;
  form2Check.value = false;
  form3Check.value = false;

  // 重置其他状态变量
  title.value = "";
  flowType.value = "";
  dbclickNodeId.value = null;

  // 关闭弹窗
  show.value = false;

  nextTick(() => {
    fitView({ duration: 300, padding: 0.1 });
  });

  if (!notify) return;
  // 通知父组件数据已重置
  emit("getNodeValue", {
    subBasinList: [],
    riverReservoirList: [],
    riverSectionList: [],
  });

  proxy.$modal.msgSuccess("节点重置成功");
};

const title = ref("");
const flowType = ref("");

// 双击节点
const handleDoubleClick = (e) => {
  let data = e.node.data;
  let { nodeType, nodeId, label } = data;
  dbclickNodeId.value = "";
  dbclickNodeId.value = nodeId;
  if (nodeType == "subBasin") {
    let index = subBasinList.value.findIndex(
      (item) => item.nodeOrder == nodeId
    );
    activeIndex.value = index;
    subBasinList.value[activeIndex.value].label = label;
  }
  if (nodeType == "reservoir") {
    let index = riverReservoirList.value.findIndex(
      (item) => item.nodeOrder == nodeId
    );
    activeIndex.value = index;
    riverReservoirList.value[activeIndex.value].label = label;
  }
  if (nodeType == "section") {
    let index = riverSectionList.value.findIndex(
      (item) => item.nodeOrder == nodeId
    );
    activeIndex.value = index;
    riverSectionList.value[activeIndex.value].label = label;
  }

  show.value = true;
  title.value = label;
  flowType.value = nodeType;
};

onInit((vueFlowInstance) => {
  // instance is the same as the return of `useVueFlow`
  vueFlowInstance.fitView();
});

// 处理节点连接
onConnect((connection) => {
  // 创建新的连接线，添加箭头标记
  const newEdge = {
    ...connection,
    markerEnd: MarkerType.ArrowClosed,
  };
  addEdges(newEdge);
});

onMounted(() => {
  getAllarea(); //获取河道断面
  getAllWater(); //获取小流域
  getAllWaterPoint(); //获取水库
});

defineExpose({
  showView,
  resetNode,
});
</script>

<template>
  <div class="dnd-flow" @drop="onDrop">
    <VueFlow
      :nodes="defaultNodes"
      :edges="defaultEdges"
      @dragover="onDragOver"
      @dragleave="onDragLeave"
      ref="dragFlow"
      @nodeDoubleClick="handleDoubleClick"
      @nodesChange="nodesChange"
      :nodes-draggable="props.showType !== 'readOnly'"
      :edges-updatable="props.showType !== 'readOnly'"
      :nodes-connectable="props.showType !== 'readOnly'"
    >
      <Controls v-if="props.showType !== 'readOnly'" />
      <Background :size="2" :gap="20" pattern-color="#BDBDBD" />
    </VueFlow>
  </div>
  <el-dialog
    v-model="show"
    :title="title"
    :modal="false"
    :show-close="false"
    style="top: 100px"
  >
    <template v-if="flowType == 'subBasin'">
      <div class="node-detail-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="小流域名称">
            {{
              subBasinList[activeIndex]?.basinName ||
              subBasinList[activeIndex]?.label ||
              "-"
            }}
          </el-descriptions-item>
          <el-descriptions-item label="小流域标识">
            {{ subBasinList[activeIndex]?.basinIdentifier || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="雨量站">
            {{ subBasinList[activeIndex]?.stationInfo || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="累计降雨量">
            {{ subBasinList[activeIndex]?.totalRainfall || 0 }}mm
          </el-descriptions-item>
          <el-descriptions-item label="累计出流量">
            {{ subBasinList[activeIndex]?.totalOutflow || 0 }}m³/s
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </template>
    <template v-if="flowType == 'reservoir'">
      <el-form
        label-width="auto"
        :model="riverReservoirList[activeIndex]"
        :rules="rules2"
        ref="form2"
        :disabled="props.showType == 'readOnly'"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="节点名称" prop="label">
              <el-input
                placeholder="请输入节点名称"
                v-model="riverReservoirList[activeIndex].label"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="对应水库" prop="reservoirCode">
              <el-select
                placeholder="请选择"
                v-model="riverReservoirList[activeIndex].reservoirCode"
                filterable
                class="w-full"
              >
                <el-option
                  :label="item.resName"
                  :value="item.registerCode"
                  v-for="item in waterPointList"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="水库标识" prop="reservoirIdentifier">
              <el-input
                placeholder="请输入"
                v-model="riverReservoirList[activeIndex].reservoirIdentifier"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库方式" prop="releaseMethod">
              <el-select
                placeholder="请选择"
                v-model="riverReservoirList[activeIndex].releaseMethod"
                class="w-full"
              >
                <el-option label="指定出流" :value="1" />
                <el-option label="自由出流" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库流量(m3/s)" prop="releaseFlow">
              <el-input-number
                placeholder="请输入"
                v-model="riverReservoirList[activeIndex].releaseFlow"
                :precision="3"
                :controls="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <!-- 河道断面 -->
    <template v-if="flowType == 'section'">
      <div class="node-detail-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="河道断面代码">
            {{ riverSectionList[activeIndex]?.riverSectionCode || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="断面标识">
            {{ riverSectionList[activeIndex]?.sectionIdentifier || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="入流断面">
            {{
              riverSectionList[activeIndex]?.isInflowSection === 0 ? "是" : "否"
            }}
          </el-descriptions-item>
          <el-descriptions-item label="洪峰流量">
            {{ riverSectionList[activeIndex]?.peakInflow || 0 }}m³/s
          </el-descriptions-item>
          <el-descriptions-item label="峰现时间" :span="2">
            {{ riverSectionList[activeIndex]?.peakInflowTime || "-" }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="show = false">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style>
@import "@vue-flow/core/dist/style.css";
@import "@vue-flow/core/dist/theme-default.css";
@import "@vue-flow/controls/dist/style.css";

.vue-flow__minimap {
  transform: scale(75%);
  transform-origin: bottom right;
}

.dnd-flow {
  position: relative;
  display: flex;
  height: 100%;
  width: 100%;
}

.dnd-flow .vue-flow-wrapper {
  flex: 1;
  height: 100%;
  width: 100%;
}

.dropzone-background {
  position: relative;
  height: 100%;
  width: 100%;
}

.dropzone-background .overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  pointer-events: none;
}

/* 确保箭头标记正确显示 */
.vue-flow__edge-path {
  stroke: #b1b1b7;
  stroke-width: 2;
}

.vue-flow__arrowhead {
  fill: #b1b1b7;
}

/* 确保连接线和箭头的层级正确 */
.vue-flow__edges {
  z-index: 1;
}

.vue-flow__nodes {
  z-index: 2;
}

/* Vue Flow 自定义节点样式 */
.rounds {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  background-color: #e1f3ff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 2px solid #5cadde;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.area1 {
  width: 100px;
  height: 45px;
  background-color: #e1f3ff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 2px solid #5cadde;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

/* 节点详情信息样式 */
.node-detail-info {
  padding: 16px 0;
}

.node-detail-info .el-descriptions {
  margin-bottom: 16px;
}

.node-detail-info .el-descriptions-item__label {
  font-weight: 600;
  color: #303133;
}

.node-detail-info .el-descriptions-item__content {
  color: #606266;
}
</style>
