<script setup>
import { reactive, onMounted, ref } from "vue";
import Map3d from "@/components/Map";
import Head from "./component/agent-head.vue";
import ChatRobot from "./component/ChatRobot/index.vue";
import AgentScheme from "./component/agent-scheme.vue";
import Panel from "./component/panel.vue";
import Contrast from "./component/contrast.vue";
import { useMapPanelStore } from "@/store/modules/map";

defineOptions({
  name: "AgentIndex",
});

const state = reactive({
  showSelf: true,
  show: false,
  height: "95%",
  // maskShow1: false,
});

const panelStore = useMapPanelStore();

const { mainPanel, comparisonPanel } = storeToRefs(panelStore);
const showSelf = ref(false);

onMounted(() => {
  window.EventBus.$on("change/right/height/max", (data) => {
    state.height = "95%";
  });
  window.EventBus.$on("change/right/height/min", (data) => {
    state.height = "auto";
  });
  window.EventBus.$on("change/agent-scheme/show", (data) => {
    showSelf.value = true;
  });
  window.EventBus.$on("change/agent-scheme/hide", (data) => {
    showSelf.value = false;
  });
  window.EventBus.$on("sliderHide", () => {
    state.show = true;
  });
  window.EventBus.$on("sliderShow", () => {
    state.show = false;
  });
});
</script>

<template>
  <div class="screen-container">
    <Head />
    <div class="content">
      <div class="map">
        <Map3d />
      </div>
      <div class="right" :style="{ height: height }" v-show="showSelf">
        <AgentScheme></AgentScheme>
      </div>
    </div>

    <ChatRobot />

    <div class="mask" v-show="mainPanel.visible || comparisonPanel.visible">
      <Panel
        v-if="mainPanel.visible"
        class="center"
      ></Panel>
      <Contrast
        v-show="comparisonPanel.visible"
        class="center"
      >
      </Contrast>
    </div>
  </div>
</template>

<style scoped lang="scss">
.screen-container {
  margin: 0;
  padding: 0 !important;
  width: 100vw;
  height: 100vh;
  background-color: #001442;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: relative;

  iframe {
    border: none;
  }
  .content {
    width: 100%;
    // height: calc(100% - 73px);
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;

    .map {
      width: 100%;
      height: 100%;
    }

    .right {
      width: 520px;
      height: 95%;
      position: absolute;
      right: 10px;
      top: 3%;

      &.hide {
        right: -520px;
      }

      .expand {
        position: absolute;
        top: 50%;
        left: -20px;
        width: 22px;
        height: 38px;
        cursor: pointer;
        z-index: 99999;
        background-image: url(./image/right.png);
        transform: rotate(180deg);
      }

      .recover {
        position: absolute;
        top: 50%;
        left: 0;
        width: 22px;
        height: 38px;
        cursor: pointer;
        color: #fff;
        z-index: 999;
        background-image: url(./image/right.png);
      }
    }
  }

  .mask {
    width: 100vw;
    height: 100vh;
    background-color: rgba($color: #000000, $alpha: 0.5);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999;

    .center {
      position: absolute;
      left: 50%;
      top: 100px;
      transform: translateX(-50%);
    }
  }
}
</style>
