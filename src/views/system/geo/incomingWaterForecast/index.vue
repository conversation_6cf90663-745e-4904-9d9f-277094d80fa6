<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="选择流域" prop="basinId">
        <el-tree-select
          v-model="queryParams.basinId"
          :data="subBasinOptions"
          :props="{ value: 'basinId', label: 'name', children: 'children' }"
          placeholder="请选择流域"
          clearable
          filterable
          style="width: 200px"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item label="选择控制断面" prop="sectionCode">
        <el-select v-model="queryParams.sectionCode" placeholder="请选择控制断面" clearable filterable>
          <el-option v-for="item in sectionOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="选择预报方案" prop="schemeId">
        <el-select v-model="queryParams.schemeId" placeholder="请选择预报方案" clearable filterable>
          <el-option v-for="item in schemeOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="选择预报时间" prop="forecastTime">
        <el-date-picker
          v-model="queryParams.forecastTime"
          type="datetime"
          placeholder="选择预报时间"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="选择推荐" prop="isRecommended">
        <el-select v-model="queryParams.isRecommended" placeholder="请选择" clearable>
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="forecastList"
        row-key="code"
        stripe
      >
        <el-table-column type="index" label="序号" width="65" align="center" :index="indexMethod" />
        <el-table-column prop="basinName" label="流域" :show-overflow-tooltip="true" align="center" />
        <el-table-column prop="sectionName" label="控制断面" :show-overflow-tooltip="true" align="center" />
        <el-table-column prop="forecastTime" label="预报时间" align="center" />
        <el-table-column prop="forecastPeriod" label="预见期（时）" align="center" />
        <el-table-column prop="peakFlow" label="洪峰流量（m³/s）" align="center" />
        <el-table-column prop="peakTime" label="峰现时间" align="center" />
        <el-table-column prop="recurrencePeriod" label="重现期（年）" align="center" />
        <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="success" icon="Star" @click="handleRecommend(scope.row)">推荐</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      :title="detailDialogTitle"
      v-model="detailDialogVisible"
      width="900px"
      append-to-body
      @close="detailTableOrChart = 'table'"
    >
      <template #header>
        <span>{{ detailDialogTitle }}</span>
      </template>
      <div>
        <div style="display: flex; justify-content: flex-end; margin-bottom: 12px;">
          <el-button size="small" @click="detailTableOrChart = detailTableOrChart === 'table' ? 'chart' : 'table'">
            {{ detailTableOrChart === 'table' ? '图' : '表' }}
          </el-button>
        </div>
        <div v-if="detailTableOrChart === 'table'">
          <el-table :data="detailData" height="400" style="width: 100%;">
            <el-table-column prop="time" label="时间" align="center" />
            <el-table-column prop="waterLevel" label="水位(m)" align="center" />
            <el-table-column prop="flow" label="流量(m³/s)" align="center" />
          </el-table>
        </div>
        <div v-else style="height: 420px;">
          <div ref="detailChartRef" style="width: 100%; height: 100%;"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { nextTick, reactive, ref, watch, getCurrentInstance, toRefs, onMounted } from "vue";
import * as echarts from 'echarts';

const { proxy } = getCurrentInstance();
const forecastList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const refreshTable = ref(true);
const total = ref(0);

const subBasinOptions = ref([
  { code: 'BASIN1', name: '流域A', basinId: 'BASIN1' },
  { code: 'BASIN2', name: '流域B', basinId: 'BASIN2', children: [{ code: 'SUB1', name: '子流域1', basinId: 'SUB1' }] }
]);

const data = reactive({
  total: 0,
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    basinId: subBasinOptions.value[0]?.basinId || '',
    sectionCode: '',
    schemeId: '',
    forecastTime: getNearestPreviousHour(),
    isRecommended: ''
  }
});
const { queryParams } = toRefs(data);

function getNearestPreviousHour() {
  const now = new Date();
  now.setMinutes(0);
  now.setSeconds(0);
  now.setMilliseconds(0);
  return now.toISOString().slice(0, 16).replace('T', ' ');
}

const sectionOptions = ref([
  { code: 'SEC1', name: '断面1' },
  { code: 'SEC2', name: '断面2' }
]);
const schemeOptions = ref([
  { id: 'SCH1', name: '方案1' },
  { id: 'SCH2', name: '方案2' }
]);
const mockForecastList = [
  { basinName: '流域A', sectionName: '断面1', forecastTime: '2023-10-01 12:00', forecastPeriod: 24, peakFlow: 500, peakTime: '2023-10-01 14:00', recurrencePeriod: 10 },
  { basinName: '流域B', sectionName: '断面2', forecastTime: '2023-10-02 12:00', forecastPeriod: 48, peakFlow: 600, peakTime: '2023-10-02 14:00', recurrencePeriod: 20 }
];

function indexMethod(index) {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
}

async function getList() {
  loading.value = true;
  forecastList.value = mockForecastList;
  total.value = mockForecastList.length;
  loading.value = false;
}

onMounted(async () => {
  await getList();
});

function handleQuery() {
  getList();
}
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

const disabledDate = (time) => {
  return time.getTime() >= Date.now();
};

// mock详情数据（时间/水位/流量）
function mockDetailDataForView() {
  const now = new Date();
  const arr = [];
  for (let i = 0; i < 24; i++) {
    const t = new Date(now.getTime() + i * 3600 * 1000);
    arr.push({
      time: t.toISOString().slice(0, 16).replace('T', ' '),
      waterLevel: +(100 + Math.random() * 10).toFixed(2),
      flow: +(500 + Math.random() * 100).toFixed(2)
    });
  }
  return arr;
}

const detailDialogVisible = ref(false);
const detailDialogTitle = ref('');
const detailTableOrChart = ref('table');
const detailData = ref([]);
const detailChartRef = ref(null);
let detailChartInstance = null;

function handleView(row) {
  detailData.value = mockDetailDataForView();
  detailDialogTitle.value = row.basinName || '详情';
  detailDialogVisible.value = true;
  nextTick(() => {
    if (detailTableOrChart.value === 'chart') updateDetailChart();
  });
}

function updateDetailChart() {
  if (!detailChartRef.value) return;
  if (detailChartInstance) {
    echarts.dispose(detailChartRef.value);
    detailChartInstance = null;
  }
  detailChartInstance = echarts.init(detailChartRef.value);
  const chartData = [...detailData.value];
  detailChartInstance.setOption({
    tooltip: { trigger: 'axis' },
    legend: { data: ['水位', '流量'] },
    grid: { left: 60, right: 60, top: 30, bottom: 70 },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.time),
      axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: 'auto' },
      name: '时间',
    },
    yAxis: [
      { type: 'value', name: '水位(m)', min: 0 },
      { type: 'value', name: '流量(m³/s)', min: 0, position: 'right', offset: 60 }
    ],
    series: [
      { name: '水位', type: 'line', data: chartData.map(item => item.waterLevel), yAxisIndex: 0, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#409EFF' } },
      { name: '流量', type: 'line', data: chartData.map(item => item.flow), yAxisIndex: 1, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#E6A23C' } }
    ],
    dataZoom: [
      { type: 'slider', show: true, xAxisIndex: 0, height: 24, bottom: 10 }
    ]
  });
  detailChartInstance.resize();
}

watch(detailTableOrChart, (val) => {
  if (val === 'chart') nextTick(updateDetailChart);
});

function handleRecommend(row) {
  proxy.$modal.loading('推荐中...');
  setTimeout(() => {
    proxy.$modal.closeLoading();
    proxy.$modal.msgSuccess('推荐成功！');
  }, 800);
}
</script>

<style scoped>
/* 样式类似历史洪水，略微调整类名 */
.forecast-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 420px;
  min-height: 320px;
  margin-top: 10px;
}
.forecast-table-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px 8px 16px 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.forecast-divider {
  width: 2px;
  background: #e5e6eb;
  margin: 0 8px;
  border-radius: 2px;
  height: 100%;
  align-self: stretch;
}
.forecast-chart-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px 16px 16px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.forecast-echart {
  width: 100%;
  height: 100%;
  min-height: 320px;
}
</style> 