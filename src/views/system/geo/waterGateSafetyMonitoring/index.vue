<template>
    <div class="app-container">
      <div class="analysis-flex-container">
        <!-- 左侧区域 -->
        <div class="analysis-flex-left">
          <!-- 头部输入框+按钮 7:3布局 -->
          <el-row :gutter="8" style="margin-bottom: 12px;">
            <el-col :span="17">
              <el-input v-model="filterText" placeholder="输入关键字过滤"  clearable />
            </el-col>
            <el-col :span="7">
              <el-button type="primary"  style="width: 100%;" @click="handleTreeFilter">查询</el-button>
            </el-col>
          </el-row>
          <!-- 树结构 -->
          <el-tree
            ref="treeRef"
            :data="treeData"
            node-key="id"
            :props="{ label: 'name', children: 'children' }"
            highlight-current
            default-expand-all
            :filter-node-method="filterNode"
            style="flex: 1; overflow: auto;"
            @node-click="handleNodeClick"
          />
        </div>
        <!-- 右侧区域：原有表格及分页内容 -->
        <div class="analysis-flex-right">
          <!-- 右侧头部工具栏 -->
          <div class="toolbar-flex-row">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
              style="width: 240px; margin-right: 12px;"
            />
            <el-button type="primary"  style="margin-right: 12px;" @click="handleSearch">查询</el-button>
            <el-button style="margin-right: 12px;" @click="handleReset">重置</el-button>
            <el-button  @click="toggleViewType" style="margin-left: auto;">
              {{ viewType === 'chart' ? '表' : '图' }}
            </el-button>
          </div>
          <!-- 内容区：图或表单独展现，占满空间 -->
          <div class="content-flex-area">
            <el-tabs v-model="activeTab" type="card" class="demo-tabs" @tab-click="handleTabClick">
              <el-tab-pane label="渗压" name="pressure">
                <div v-if="viewType === 'chart'" ref="chartRef" class="chart-container" ></div>
                <el-table
                  v-else
                  :data="accuracyList"
                  row-key="id"
                  stripe
                  class="flex-full-table"
                  v-loading="loading"
                >
                  <el-table-column type="index" label="序号" width="65" align="center" :index="indexMethod" />
                  <el-table-column prop="schemeName" label="预报方案" :show-overflow-tooltip="true" align="center" />
                  <el-table-column prop="floodHistoryCode" label="历史洪水编号" :show-overflow-tooltip="true" align="center" />
                  <el-table-column prop="basinName" label="所属流域" align="center" />
                  <el-table-column prop="flowRelativeError" label="洪量相对误差(%)" align="center" />
                  <el-table-column prop="peakRelativeError" label="洪峰流量相对误差(%)" align="center" />
                  <el-table-column prop="peakTimeLag" label="峰现时延(小时)" align="center" />
                  <el-table-column prop="certaintyCoefficient" label="确定性系数" align="center" />
                  <el-table-column prop="result" label="评价结果" align="center">
                    <template #default="scope">
                      <span class="apple-glass-tag" :class="{'apple-glass-tag--excellent': scope.row.result === 1, 'apple-glass-tag--bad': scope.row.result === 0}">
                        {{ scope.row.result === 1 ? '合格' : '不合格' }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="time" label="评价时间" align="center" width="160"/>
                  
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="温度" name="temperature">
                <div v-if="viewType === 'chart'" ref="temperatureChartRef" class="chart-container" ></div>
                <el-table
                  v-else
                  :data="accuracyList"
                  row-key="id"
                  stripe
                  class="flex-full-table"
                  v-loading="loading"
                >
                  <el-table-column type="index" label="序号" width="65" align="center" :index="indexMethod" />
                  <el-table-column prop="schemeName" label="预报方案" :show-overflow-tooltip="true" align="center" />
                  <el-table-column prop="floodHistoryCode" label="历史洪水编号" :show-overflow-tooltip="true" align="center" />
                  <el-table-column prop="basinName" label="所属流域" align="center" />
                  <el-table-column prop="flowRelativeError" label="洪量相对误差(%)" align="center" />
                  <el-table-column prop="peakRelativeError" label="洪峰流量相对误差(%)" align="center" />
                  <el-table-column prop="peakTimeLag" label="峰现时延(小时)" align="center" />
                  <el-table-column prop="certaintyCoefficient" label="确定性系数" align="center" />
                  <el-table-column prop="result" label="评价结果" align="center">
                    <template #default="scope">
                      <span class="apple-glass-tag" :class="{'apple-glass-tag--excellent': scope.row.result === 1, 'apple-glass-tag--bad': scope.row.result === 0}">
                        {{ scope.row.result === 1 ? '合格' : '不合格' }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="time" label="评价时间" align="center" width="160"/>
                  
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
          <pagination v-if="viewType === 'table'"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
          <!-- 原有表格及分页内容结束 -->
        </div>
      </div>

      <!-- 新增弹窗：新增预报任务 -->

    
    </div>
  </template>

  <script setup>
  import { onMounted, onUnmounted, nextTick, reactive, ref, watch, computed, toRefs } from "vue";
  import * as echarts from 'echarts';
  import { getAccuracyEvaluationList } from '@/api/watershed/forecast/index.js';
  import moment from 'moment'
  import { getSafetyMonitorTree, getSafetyMonitorPointInfo } from '@/api/waterGate/index.js';
  import { ElMessage } from 'element-plus';
  defineOptions({
    name: "TownshipIndex",
  });
  const accuracyList = ref([])
  const total = ref(0)
  const loading = ref(false);



  // 查询参数对象
  const queryParams = reactive({
    id: '', // 安全检测点id
    monitorItem: '0', // 监测项，'0'渗压，'1'温度
    startTime: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm'),
    endTime: moment().format('YYYY-MM-DD HH:mm')
  });

  // 时间选择器绑定
  const chartDateRange = ref([
    queryParams.startTime,
    queryParams.endTime
  ]);

  // 监听时间选择器变化，更新queryParams
  function handleChartDateChange(val) {
    if (Array.isArray(val) && val.length === 2) {
      queryParams.startTime = val[0];
      queryParams.endTime = val[1];
    }
  }

  // 监听tab切换，切换监测项
  function handleTabClick(tab, event) {
    if (tab.name == 'pressure') {
      queryParams.monitorItem = '0';
    } else if (tab.name == 'temperature') {
      queryParams.monitorItem = '1';
    }
    if (viewType.value === 'chart') {
      nextTick(() => {
        setTimeout(() => {
          renderChart();
          handleChartResize();
        }, 0);
      });
    }
  }

  function indexMethod(index) {
    return (queryParams.pageNum - 1) * queryParams.pageSize + index + 1;
  }

  async function fetchSafetyMonitorTree() {
    try {
      const res = await getSafetyMonitorTree({})
      treeData.value = res.rows || []
    } catch (e) {
      treeData.value = []
    }
  }

  onMounted(async () => {
    await fetchSafetyMonitorTree();
  });
  // 获取精度评价列表
  async function getList() {
    loading.value = true;
    try {
      const params = {
        pageNum: queryParams.pageNum,
        pageSize: queryParams.pageSize,
      };
      if (queryParams.forecastSchemeId) {
        params.schemeId = queryParams.forecastSchemeId;
      }
      const res = await getAccuracyEvaluationList(params);
      if (res && Array.isArray(res.rows)) {
        accuracyList.value = res.rows;
        total.value = res.total || 0;
      } else {
        accuracyList.value = [];
        total.value = 0;
      }
    } catch (e) {
      accuracyList.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  }

  // 左侧树过滤相关
  const filterText = ref('');
  const treeRef = ref(null);

  const treeData = ref([])

  function filterNode(value, data) {
    if (!value) return true;
    return data.label && data.label.indexOf(value) !== -1;
  }

  function handleTreeFilter() {
    if (treeRef.value) {
      treeRef.value.filter(filterText.value);
    }
  }

  // 支持输入时自动过滤
  watch(filterText, (val) => {
    handleTreeFilter();
  });

  // 右侧头部相关变量
  const dateRange = ref([
    moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm'),
    moment().format('YYYY-MM-DD HH:mm')
  ])
  const viewType = ref('chart') // 'chart' or 'table'
  const activeTab = ref('pressure') // 'pressure' or 'temperature'


  function toggleViewType() {
    viewType.value = viewType.value === 'chart' ? 'table' : 'chart'
  }
  // 查询按钮点击
  async function handleSearch() {
    // 校验参数
    if (!queryParams.id || !queryParams.monitorItem || !queryParams.startTime || !queryParams.endTime) {
      ElMessage.warning('请选择安全检测点、监测项和时间范围');
      return;
    }
    loading.value = true;
    // 用 moment.js 格式化时间，秒补全为 00
    const startTime = moment(queryParams.startTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm:00');
    const endTime = moment(queryParams.endTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm:00');
    try {
      const res = await getSafetyMonitorPointInfo({
        id: queryParams.id,
        monitorItem: queryParams.monitorItem,
        startTime, // 传递格式化后的时间
        endTime
      });
      if (res && Array.isArray(res.rows)) {
        // 渲染到图和表格
        accuracyList.value = res.rows;
        total.value = res.total || 0;
        // 你可以在这里处理图表数据源
        // chartData/temperatureChartData = res.rows.map(...)
      } else {
        accuracyList.value = [];
        total.value = 0;
      }
    } catch (e) {
      accuracyList.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  }
  function handleReset() {
    dateRange.value = [
      moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm'),
      moment().format('YYYY-MM-DD HH:mm')
    ]
    currentNodeKey.value = null
    treeRef.value && treeRef.value.setCurrentKey(null)
    // handleSearch()
  }
  // 图表相关
  const chartRef = ref(null);
  const temperatureChartRef = ref(null);
  let chartInstance = null;
  let temperatureChartInstance = null;
  // mock数据
  const chartData = [
    { time: '2024-06-01 10:00', pressure: 120, rainfall: 10 },
    { time: '2024-06-01 11:00', pressure: 130, rainfall: 15 },
    { time: '2024-06-01 12:00', pressure: 110, rainfall: 8 },
    { time: '2024-06-01 13:00', pressure: 140, rainfall: 0 },
    { time: '2024-06-01 14:00', pressure: 135, rainfall: 5 }
  ];
  const temperatureChartData = [
    { time: '2024-06-01 10:00', temperature: 25, humidity: 60 },
    { time: '2024-06-01 11:00', temperature: 26, humidity: 58 },
    { time: '2024-06-01 12:00', temperature: 24, humidity: 62 },
    { time: '2024-06-01 13:00', temperature: 27, humidity: 55 },
    { time: '2024-06-01 14:00', temperature: 26, humidity: 57 }
  ];
  function renderChart() {
    if (activeTab.value === 'pressure' && !chartRef.value) return;
    if (activeTab.value === 'temperature' && !temperatureChartRef.value) return;
    if (activeTab.value === 'pressure') {
      if (!chartInstance) {
        chartInstance = echarts.init(chartRef.value);
      }
      const xData = chartData.map(item => item.time);
      const pressureData = chartData.map(item => item.pressure);
      const rainfallData = chartData.map(item => item.rainfall);
      chartInstance.setOption({
        tooltip: { trigger: 'axis' },
        legend: { data: ['渗压(kpa)', '降雨量(mm)'] },
        grid: { left: 50, right: 50, top: 30, bottom: 80 },
        xAxis: [
          { type: 'category', data: xData, axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: 'auto' }, name: '时间' }
        ],
        yAxis: [
          { type: 'value', name: '渗压(kpa)', min: 0 },
          { type: 'value', name: '降雨量(mm)', min: 0, position: 'right' }
        ],
        series: [
          { name: '渗压(kpa)', type: 'line', data: pressureData, yAxisIndex: 0, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#409EFF' } },
          { name: '降雨量(mm)', type: 'line', data: rainfallData, yAxisIndex: 1, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#E6A23C' } }
        ],
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            start: 0,
            end: 100,
            height: 24,
            bottom: 10
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            start: 0,
            end: 100
          }
        ]
      });
      chartInstance.resize();
    } else if (activeTab.value === 'temperature') {
      if (!temperatureChartInstance) {
        temperatureChartInstance = echarts.init(temperatureChartRef.value);
      }
      const xData = temperatureChartData.map(item => item.time);
      const temperatureData = temperatureChartData.map(item => item.temperature);
      const humidityData = temperatureChartData.map(item => item.humidity);
      temperatureChartInstance.setOption({
        tooltip: { trigger: 'axis' },
        legend: { data: ['温度(℃)', '湿度(%)'] },
        grid: { left: 50, right: 50, top: 30, bottom: 40 },
        xAxis: [
          { type: 'category', data: xData, axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: 'auto' }, name: '时间' }
        ],
        yAxis: [
          { type: 'value', name: '温度(℃)', min: 0 },
          { type: 'value', name: '湿度(%)', min: 0, position: 'right' }
        ],
        series: [
          { name: '温度(℃)', type: 'line', data: temperatureData, yAxisIndex: 0, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#FF5733' } },
          { name: '湿度(%)', type: 'line', data: humidityData, yAxisIndex: 1, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#6C757D' } }
        ],
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            start: 0,
            end: 100,
            height: 24,
            bottom: 10
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            start: 0,
            end: 100
          }
        ]
      });
      temperatureChartInstance.resize();
    }
  }
  function handleChartResize() {
    if (viewType.value === 'chart') {
      if (activeTab.value === 'pressure' && chartInstance) chartInstance.resize();
      if (activeTab.value === 'temperature' && temperatureChartInstance) temperatureChartInstance.resize();
    }
  }
  // 切换图/表时初始化图表
  watch(viewType, (val) => {
    if (val === 'chart') {
      nextTick(() => {
        setTimeout(() => {
          if (chartInstance) {
            chartInstance.dispose()
            chartInstance = null
          }
          renderChart()
        }, 0);
      })
    }
  })
  onMounted(() => {
    nextTick(() => {
      setTimeout(() => {
        if (viewType.value === 'chart') {
          renderChart()
        }
        window.addEventListener('resize', handleChartResize)
      }, 0);
    })
  })
  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
    if (temperatureChartInstance) {
      temperatureChartInstance.dispose()
      temperatureChartInstance = null
    }
    window.removeEventListener('resize', handleChartResize)
  })

  // 新增当前选中节点id
  const currentNodeKey = ref(null)
  // 只允许子节点被选中
  function handleNodeClick(data, node) {
    if (!data.children || data.children.length === 0) {
      currentNodeKey.value = data.id
      treeRef.value && treeRef.value.setCurrentKey(data.id)
      queryParams.id = data.id;
    } else {
      currentNodeKey.value = null
      treeRef.value && treeRef.value.setCurrentKey(null)
      queryParams.id = '';
    }
  }
  </script>

  <style scoped>
  .rainfall-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 420px;
  min-height: 320px;
  margin-top: 10px;
  }
  .rainfall-table-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px 8px 16px 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  }
  .rainfall-divider {
  width: 2px;
  background: #e5e6eb;
  margin: 0 8px;
  border-radius: 2px;
  height: 100%;
  align-self: stretch;
  }
  .rainfall-chart-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px 16px 16px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  }
  .rainfall-echart {
  width: 100%;
  height: 100%;
  min-height: 320px;
  }
  .apple-glass-tag {
    display: inline-block;
    min-width: 64px;
    padding: 0 22px;
    height: 32px;
    line-height: 30px;
    font-size: 15px;
    font-weight: 600;
    border-radius: 999px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
    color: #fff;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10);
    user-select: none;
    cursor: default;
    position: relative;
    overflow: hidden;
    transition: box-shadow 0.3s;
    animation: apple-breath 2.4s infinite cubic-bezier(.4,0,.2,1);
  }
  .apple-glass-tag--excellent {
    background: linear-gradient(90deg, #4ADE80 0%, #22D3EE 100%);
    box-shadow: 0 0 16px 0 #4ADE8044;
  }
  .apple-glass-tag--good {
    background: linear-gradient(90deg, #60A5FA 0%, #2563EB 100%);
    box-shadow: 0 0 16px 0 #60A5FA44;
  }
  .apple-glass-tag--normal {
    background: linear-gradient(90deg, #FBBF24 0%, #F59E42 100%);
    box-shadow: 0 0 16px 0 #FBBF2444;
  }
  .apple-glass-tag--bad {
    background: linear-gradient(90deg, #F87171 0%, #F43F5E 100%);
    box-shadow: 0 0 16px 0 #F8717144;
  }
  @keyframes apple-breath {
    0%, 100% { box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10); opacity: 1; }
    50% { box-shadow: 0 8px 32px 0 rgba(0,0,0,0.18); opacity: 0.92; }
  }
  .analysis-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.analysis-flex-left {
  width: 300px;
  min-width: 220px;
  max-width: 340px;
  background: #fff;
  box-shadow: 2px 0 8px rgba(0,0,0,0.03);
  padding: 12px 8px 12px 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.analysis-flex-right {
  flex: 1;
  min-width: 0;
  padding-left: 18px;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.toolbar-flex-row {
  display: flex;
  align-items: center;
  width: 100%;
  height: 48px;
  line-height: 48px;
  margin-bottom: 12px;
}
.content-flex-area {
  flex: 1 1 0%;
  width: 100%;

  min-height: 0;
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 700px; /* 设置基础高度，防止初始渲染宽高为0 */
  flex: 1 1 0%;
}
.el-table.flex-half-table {
  flex: 1 1 0%;
  min-height: 180px;
}
.flex-full-table {
  width: 100%;
  height: 100%;
  min-height: 180px;
}
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: transparent !important;
}
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content .el-tree-node__label) {
  background-color: #fff3cd;
  border-radius: 12px;
  padding: 6px 6px;
}
  </style> 